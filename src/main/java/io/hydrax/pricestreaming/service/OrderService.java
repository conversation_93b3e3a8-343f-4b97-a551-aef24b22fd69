package io.hydrax.pricestreaming.service;

import com.google.protobuf.TextFormat;
import io.hydrax.aeron.client.ClientManager;
import io.hydrax.aeron.common.Topic;
import io.hydrax.pricestreaming.cache.MarketCache;
import io.hydrax.pricestreaming.cache.OrderCache;
import io.hydrax.pricestreaming.cache.TradingVenueCache;
import io.hydrax.pricestreaming.common.OrderType;
import io.hydrax.pricestreaming.common.TimeInForceEnum;
import io.hydrax.pricestreaming.domain.ERResponseList;
import io.hydrax.pricestreaming.domain.Order;
import io.hydrax.pricestreaming.domain.PlaceOrder;
import io.hydrax.pricestreaming.domain.TradingVenueAccountDTO;
import io.hydrax.pricestreaming.exception.RejectionException;
import io.hydrax.pricestreaming.router.RoutingEngine;
import io.hydrax.pricestreaming.utils.ExceptionUtil;
import io.hydrax.pricestreaming.utils.IdUtil;
import io.hydrax.pricestreaming.utils.TopicUtil;
import io.hydrax.proto.metwo.match.*;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class OrderService {

  final ClientManager clientManager;
  final OrderCache orderCache;
  final TradingVenueCache tradingVenueCache;
  final RoutingEngine routingEngine;
  final TradingVenueAccountService tradingVenueAccountService;
  final MarketCache marketCache;

  public PsParentOrderExecReport generateParentOrder(PsOrder order) {
    if (log.isTraceEnabled()) {
      log.trace(
          "[*SMART_ORDER_ROUTER*] generateParentOrder: {}", TextFormat.shortDebugString(order));
    }
    Long longParentOrderId = IdUtil.formatId(order.getOrderId());
    orderCache.put(longParentOrderId, order);

    // Store earmark fee and tax
    orderCache.putParentOrderRemainingEarmark(
        longParentOrderId, "remainingEarmarkFee", order.getEarmarkFeeAmt());
    orderCache.putParentOrderRemainingEarmark(
        longParentOrderId, "remainingEarmarkTax", order.getEarmarkTaxAmt());
    return buildOrderMessage(order);
  }

  private static PsParentOrderExecReport buildOrderMessage(PsOrder order) {
    if (log.isTraceEnabled()) {
      log.trace(
          "[*SMART_ORDER_ROUTER*] Build ps parent order buildOrderMessage, order: {}",
          TextFormat.shortDebugString(order));
    }
    return PsParentOrderExecReport.newBuilder()
        .setFromService("sor")
        .setEarmarkAmt(order.getEarmarkAmt())
        .setEstimatedFees(order.getEarmarkFeeAmt())
        .setEstimatedTax(order.getEarmarkTaxAmt())
        .setExternalOrderId(order.getClOrdId())
        .setPsSenderService(PsSenderService.PS_SENDER_SERVICE_SOR)
        .setOrderStatus(PsParentOrderStatus.PS_PARENT_ORDER_STATUS_PENDING)
        .setBaseBalanceAccountId(order.getBaseBalanceAccountId())
        .setQuoteBalanceAccountId(order.getQuoteBalanceAccountId())
        .setPsOrderType(order.getOrdType())
        .setAssetHoldingAccountId(order.getHoldingAccountId())
        .setOrderId(order.getOrderId())
        .setQuantity(order.getQty())
        .setPrice(order.getPrice())
        .setSide(order.getSide())
        .setSymbol(order.getSymbol())
        .setProcessedTime(System.currentTimeMillis())
        .setServiceAccountId(order.getServiceAccountId())
        .setTimeInForce(order.getTimeInForce())
        .setTraceId(order.getTraceId())
        .setUserId(order.getUserId())
        .setPremium(order.getPremium())
        .setExpiryTime(order.getExpireTime())
        .setRequestReceivedTime(order.getRequestReceivedTime())
        .setFixClientOrderId(order.getFixClientOrderId())
        .setFixSenderCompId(order.getFixSenderCompId())
        .build();
  }

  public void cancelOrder(PsOrder order) {
    try {
      checkOrderExists(order);
      List<String> childOrderId = orderCache.getChildOrderList(IdUtil.formatId(order.getOrderId()));
      for (String id : childOrderId) {
        String venueCode = orderCache.getChildOrder(id);
        // query lp ticker name
        String lpTickerName =
            tradingVenueCache.getLpTickerName(order.getMarketCode(), venueCode, order.getSymbol());
        String marketModel = marketCache.getMarketModelBySymbolCode(order.getSymbol());
        TradingVenueAccountDTO venueAccount =
            tradingVenueAccountService.getTradingVenueAccount(venueCode, order, marketModel);
        PsOrder cancelOrder =
            order.toBuilder()
                .setVenueAccountName(venueAccount.getVenueAccount())
                .setOrderId(id)
                .setOrigClOrdId(id)
                .setFixRequestId(id)
                .setClOrdId(id)
                .setSymbol(lpTickerName)
                .setRequestType(RequestType.REQUEST_TYPE_CANCEL_ORDER)
                .build();
        clientManager.send(
            PlaceOrder.builder()
                .topic(TopicUtil.create(Topic.ORDER, venueCode))
                .request(Request.newBuilder().setPsOrder(cancelOrder).build())
                .build());
        if (log.isTraceEnabled()) {
          log.trace(
              "[*SMART_ORDER_ROUTER*] generateCancelOrder order: {}, venueCode: {}",
              TextFormat.shortDebugString(order),
              venueCode);
        }
      }
    } catch (RejectionException e) {
      log.error("order cancel rejected! {}", e.getMessage(), e);
      ERResponseList erResponseList = ExceptionUtil.buildRejectionER(order, e.getMessage());
      clientManager.send(erResponseList);
    } catch (Exception e) {
      log.error("order cancel router thrown exception! {}", e.getMessage(), e);
    }
  }

  public void placeOrder(Order order) {
    try {
      validateOrder(order.getPsOrder());
      List<String> tradingVenues = marketFilter(order.getPsOrder());
      if (tradingVenues.isEmpty()) {
        if (log.isTraceEnabled()) {
          log.trace(
              "No trading venue found for order: {}",
              TextFormat.shortDebugString(order.getPsOrder()));
        }
        throw new RejectionException(
            "Order placement not allowed due to market state or invalid order type/TIF");
      }
      routingEngine.route(order, tradingVenues);
    } catch (RejectionException e) {
      log.error("order rejected! {}", e.getMessage(), e);
      ERResponseList erResponseList =
          ExceptionUtil.buildRejectionER(order.getPsOrder(), e.getMessage());
      clientManager.send(erResponseList);
    } catch (Exception e) {
      log.error("order router thrown exception! {}", e.getMessage(), e);

      ERResponseList erResponseList =
          ExceptionUtil.buildRejectionER(
              order.getPsOrder(), "Internal Error, please contact support");
      clientManager.send(erResponseList);
    }
  }

  private void validateOrder(PsOrder order) {
    //    duplicateCheck(order);
    marketValidate(order);
  }

  private void duplicateCheck(PsOrder order) {
    if (!orderCache.add(order.getClOrdId())) {
      throw new RejectionException("Duplicate Order: " + order.getClOrdId());
    }
  }

  List<String> marketFilter(PsOrder order) {
    if (log.isTraceEnabled()) {
      log.trace("Market filter: {}", tradingVenueCache.getAll());
    }
    return tradingVenueCache.selectCodeByTimeInForceAndOrderType(
        TimeInForceEnum.from(order.getTimeInForce().getNumber()).getTifDB(),
        OrderType.from(order.getOrdType().getNumber()).getName(),
        order.getSymbol());
  }

  private void marketValidate(PsOrder order) {}

  private void checkOrderExists(PsOrder order) {
    if (null == orderCache.getParentOrder(IdUtil.formatId(order.getOrderId()))) {
      throw new RejectionException("Order not found");
    }
  }
}
